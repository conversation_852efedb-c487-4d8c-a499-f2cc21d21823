{"version": 3, "file": "PDFObjectParser.js", "sourceRoot": "", "sources": ["../../../src/core/parser/PDFObjectParser.ts"], "names": [], "mappings": ";AAAA,OAAO,EACL,qBAAqB,EACrB,qBAAqB,EAErB,0BAA0B,GAC3B,kBAAwB;AACzB,OAAO,QAAQ,4BAAkC;AACjD,OAAO,OAAO,2BAAiC;AAC/C,OAAO,OAAoB,2BAAiC;AAC5D,OAAO,YAAY,gCAAsC;AACzD,OAAO,OAAO,2BAAiC;AAC/C,OAAO,OAAO,2BAAiC;AAC/C,OAAO,SAAS,6BAAmC;AAEnD,OAAO,YAAY,gCAAsC;AACzD,OAAO,MAAM,0BAAgC;AAE7C,OAAO,SAAS,6BAAmC;AACnD,OAAO,UAAU,qBAAmC;AACpD,OAAO,UAAU,qBAAmC;AAEpD,OAAO,UAAU,iCAAuC;AACxD,OAAO,WAAW,kCAAwC;AAC1D,OAAO,WAAW,kCAAwC;AAC1D,OAAO,SAAS,4BAAkC;AAClD,OAAO,EAAE,WAAW,EAAE,6BAAmC;AACzD,OAAO,EAAE,QAAQ,EAAE,2BAAiC;AACpD,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,0BAAgC;AAC7D,OAAO,EAAE,YAAY,EAAE,6BAAmC;AAC1D,OAAO,EAAE,YAAY,EAAE,oBAAkB;AAEzC,uEAAuE;AACvE;IAA8B,mCAAU;IAetC,yBAAY,UAAsB,EAAE,OAAmB,EAAE,UAAkB;QAAlB,2BAAA,EAAA,kBAAkB;QAA3E,YACE,kBAAM,UAAU,EAAE,UAAU,CAAC,SAE9B;QADC,KAAI,CAAC,OAAO,GAAG,OAAO,CAAC;;IACzB,CAAC;IAED,uEAAuE;IACvE,qCAAW,GAAX;QACE,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEjC,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;YAAE,OAAO,OAAO,CAAC,IAAI,CAAC;QAC1D,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,OAAO,CAAC,KAAK,CAAC;QAC5D,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;YAAE,OAAO,OAAO,CAAC;QAErD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;QAE/B,IACE,IAAI,KAAK,SAAS,CAAC,QAAQ;YAC3B,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,QAAQ,EAC9C;YACA,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACjC;QACD,IAAI,IAAI,KAAK,SAAS,CAAC,QAAQ;YAAE,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;QAC9D,IAAI,IAAI,KAAK,SAAS,CAAC,SAAS;YAAE,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;QAC5D,IAAI,IAAI,KAAK,SAAS,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;QAC7D,IAAI,IAAI,KAAK,SAAS,CAAC,iBAAiB;YAAE,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;QACnE,IAAI,SAAS,CAAC,IAAI,CAAC;YAAE,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAEpD,MAAM,IAAI,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC;IAC/D,CAAC;IAES,0CAAgB,GAA1B;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACvC,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEjC,IAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QAC3C,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE;YAC9B,IAAM,SAAS,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACxC,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,SAAS,CAAC,CAAC,EAAE;gBACrC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBACnC,OAAO,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;aACvC;SACF;QAED,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAClC,OAAO,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC;IAED,sFAAsF;IAC5E,wCAAc,GAAxB;QACE,IAAI,KAAK,GAAG,EAAE,CAAC;QAEf,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC1C,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,SAAS,CAAC,WAAW,EAAE;YACxE,KAAK,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;SAC1C;QACD,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAE7C,OAAO,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IAES,qCAAW,GAArB;QACE,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,KAAK,GAAG,EAAE,CAAC;QAEf,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE;YACzB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAC/B,KAAK,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;YAE5B,kCAAkC;YAClC,IAAI,CAAC,SAAS,EAAE;gBACd,IAAI,IAAI,KAAK,SAAS,CAAC,SAAS;oBAAE,UAAU,IAAI,CAAC,CAAC;gBAClD,IAAI,IAAI,KAAK,SAAS,CAAC,UAAU;oBAAE,UAAU,IAAI,CAAC,CAAC;aACpD;YAED,0DAA0D;YAC1D,IAAI,IAAI,KAAK,SAAS,CAAC,SAAS,EAAE;gBAChC,SAAS,GAAG,CAAC,SAAS,CAAC;aACxB;iBAAM,IAAI,SAAS,EAAE;gBACpB,SAAS,GAAG,KAAK,CAAC;aACnB;YAED,yEAAyE;YACzE,IAAI,UAAU,KAAK,CAAC,EAAE;gBACpB,8DAA8D;gBAC9D,OAAO,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;aAC3D;SACF;QAED,MAAM,IAAI,0BAA0B,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC9D,CAAC;IAED,8EAA8E;IAC9E,oEAAoE;IAC1D,mCAAS,GAAnB;QACE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAE9C,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE;YACzB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAC/B,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC;gBAAE,MAAM;YACnD,IAAI,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;SACnB;QAED,OAAO,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAES,oCAAU,GAApB;QACE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;QACnD,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEjC,IAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,SAAS,CAAC,kBAAkB,EAAE;YACzD,IAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACnC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,IAAI,CAAC,yBAAyB,EAAE,CAAC;SAClC;QACD,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QACpD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAES,mCAAS,GAAnB;QACE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC1C,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC1C,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEjC,IAAM,IAAI,GAAY,IAAI,GAAG,EAAE,CAAC;QAEhC,OACE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;YAClB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,SAAS,CAAC,WAAW;YAC3C,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,WAAW,EACjD;YACA,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAC7B,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACjC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACrB,IAAI,CAAC,yBAAyB,EAAE,CAAC;SAClC;QAED,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC7C,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAE7C,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;QAE1C,IAAI,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE;YAClC,OAAO,UAAU,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SAC1D;aAAM,IAAI,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE;YACvC,OAAO,WAAW,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SAC3D;aAAM,IAAI,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE;YACtC,OAAO,WAAW,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SAC3D;aAAM;YACL,OAAO,OAAO,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SACvD;IACH,CAAC;IAES,2CAAiB,GAA3B;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;QAEvC,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAE9B,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEjC,IACE,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC;YACvC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC;YACvC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC;YACvC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC;YACvC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EACnC;YACA,OAAO,IAAI,CAAC;SACb;QAED,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QAClC,IAAI,GAAW,CAAC;QAEhB,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC9C,IAAI,MAAM,YAAY,SAAS,EAAE;YAC/B,GAAG,GAAG,KAAK,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YAChC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;gBAC1C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACzB,GAAG,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;aAC9C;SACF;aAAM;YACL,GAAG,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;SAC9C;QAED,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAE9C,OAAO,YAAY,CAAC,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACzC,CAAC;IAES,iDAAuB,GAAjC,UAAkC,QAAkB;QAClD,uDAAuD;QACvD,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QAE9B,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE;YACzB,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAE1B,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBACtC,UAAU,IAAI,CAAC,CAAC;aACjB;iBAAM,IACL,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,aAAa,CAAC;gBACzC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,aAAa,CAAC;gBACzC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,aAAa,CAAC;gBACzC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,EACrC;gBACA,UAAU,IAAI,CAAC,CAAC;aACjB;iBAAM;gBACL,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;aACnB;YAED,IAAI,UAAU,KAAK,CAAC;gBAAE,MAAM;SAC7B;QAED,IAAI,UAAU,KAAK,CAAC;YAAE,MAAM,IAAI,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAEhE,OAAO,GAAG,CAAC;IACb,CAAC;IA7OM,wBAAQ,GAAG,UAChB,KAAiB,EACjB,OAAmB,EACnB,UAAoB,IACjB,OAAA,IAAI,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,UAAU,CAAC,EAA9D,CAA8D,CAAC;IAE7D,6BAAa,GAAG,UACrB,UAAsB,EACtB,OAAmB,EACnB,UAAkB;QAAlB,2BAAA,EAAA,kBAAkB;QACf,OAAA,IAAI,eAAe,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC;IAApD,CAAoD,CAAC;IAoO5D,sBAAC;CAAA,AA/OD,CAA8B,UAAU,GA+OvC;AAED,eAAe,eAAe,CAAC"}