const express = require("express");
const OrderController = require("../controllers/orderController");
const { verifyTokenFromCookie } = require("../middleware/jwtCookieMiddleware");

const router = express.Router();
const orderController = new OrderController();

// All order routes require authentication
router.use(verifyTokenFromCookie);

// Get user's order statistics
// GET /api/orders/stats
router.get("/stats", orderController.getUserOrderStats);

// Get user's tickets (formatted for dashboard)
// GET /api/orders/tickets
router.get("/tickets", orderController.getUserTickets);

// Get user's orders
// GET /api/orders
router.get("/", orderController.getUserOrders);

// Create order from cart with attendee information
// POST /api/orders/create-from-cart
router.post("/create-from-cart", orderController.createOrderFromCart);

// Get specific order details
// GET /api/orders/:orderId
router.get("/:orderId", orderController.getOrderById);

module.exports = router;
