const { PrismaClient } = require('@prisma/client');
const TicketService = require('./ticketService');

const prisma = new PrismaClient();

class OrderProcessingService {
  static instance = null;

  static getInstance() {
    if (!OrderProcessingService.instance) {
      OrderProcessingService.instance = new OrderProcessingService();
    }
    return OrderProcessingService.instance;
  }

  constructor() {
    this.ticketService = TicketService.getInstance();
  }

  /**
   * Get user ID from account ID
   */
  async getUserIdFromAccountId(accountId) {
    try {
      const user = await prisma.users.findUnique({
        where: { account_id: accountId },
        select: { user_id: true }
      });

      if (!user) {
        throw new Error('User not found');
      }

      return user.user_id;
    } catch (error) {
      console.error('Error getting user ID:', error);
      throw new Error('Failed to get user information');
    }
  }

  /**
   * Create order from cart with attendee information
   */
  async createOrderFromCart(accountId, attendeeInfoArray, paymentInfo = {}) {
    const transaction = await prisma.$transaction(async (prisma) => {
      try {
        // Get user ID
        const userId = await this.getUserIdFromAccountId(accountId);

        // Get cart items with full details
        const cartItems = await prisma.cart.findMany({
          where: { user_id: userId },
          include: {
            tickettypes: {
              include: {
                events: {
                  include: {
                    locations: true
                  }
                },
                eventcategories: true
              }
            }
          }
        });

        if (cartItems.length === 0) {
          throw new Error('Cart is empty');
        }

        // Calculate total amount
        const subtotal = cartItems.reduce((total, item) => {
          return total + (parseFloat(item.tickettypes.price) * item.quantity);
        }, 0);

        const organizerFees = subtotal * 0.05; // 5% organizer fee
        const serviceFees = subtotal * 0.1; // 10% service fee
        const totalAmount = subtotal + organizerFees + serviceFees;

        // Create order
        const order = await prisma.orders.create({
          data: {
            user_id: userId,
            total_amount: totalAmount,
            additional_fees: organizerFees + serviceFees,
            payment_status: paymentInfo.status || 'completed', // For now, assume completed
            payment_method: paymentInfo.method || 'card',
            transaction_id: paymentInfo.transactionId || `txn_${Date.now()}`
          }
        });

        // Create order items and tickets
        const createdTickets = [];
        const orderItems = [];

        for (const cartItem of cartItems) {
          // Create order item
          const orderItem = await prisma.orderitems.create({
            data: {
              order_id: order.order_id,
              ticket_type_id: cartItem.ticket_type_id,
              quantity: cartItem.quantity,
              unit_price: cartItem.tickettypes.price
            }
          });

          orderItems.push(orderItem);

          // Find attendee info for this ticket type
          const attendeeInfoForTicketType = attendeeInfoArray.filter(
            info => info.ticketTypeId === cartItem.ticket_type_id
          );

          // Create individual tickets based on quantity
          for (let i = 0; i < cartItem.quantity; i++) {
            const attendeeInfo = attendeeInfoForTicketType[i];
            
            if (!attendeeInfo) {
              throw new Error(`Missing attendee information for ticket type ${cartItem.ticket_type_id}`);
            }

            // Prepare ticket data
            const ticketData = {
              orderId: order.order_id,
              ticketTypeId: cartItem.ticket_type_id,
              attendeeName: attendeeInfo.attendeeInfo.name,
              attendeeEmail: attendeeInfo.attendeeInfo.email,
              attendeePhone: attendeeInfo.attendeeInfo.phone,
              eventData: cartItem.tickettypes.events,
              ticketTypeData: cartItem.tickettypes,
              categoryData: cartItem.tickettypes.eventcategories
            };

            // Create ticket with PDF
            const ticketResult = await this.ticketService.createTicket(ticketData);
            createdTickets.push(ticketResult);
          }

          // Update ticket type availability
          await prisma.tickettypes.update({
            where: { ticket_type_id: cartItem.ticket_type_id },
            data: {
              quantity_available: {
                decrement: cartItem.quantity
              }
            }
          });
        }

        // Clear cart after successful order creation
        await prisma.cart.deleteMany({
          where: { user_id: userId }
        });

        return {
          order,
          orderItems,
          tickets: createdTickets,
          totalAmount,
          subtotal,
          fees: organizerFees + serviceFees
        };

      } catch (error) {
        console.error('Error in order creation transaction:', error);
        throw error;
      }
    });

    return transaction;
  }

  /**
   * Get order details with tickets
   */
  async getOrderWithTickets(orderId, userId) {
    try {
      const order = await prisma.orders.findFirst({
        where: {
          order_id: orderId,
          user_id: userId
        },
        include: {
          orderitems: {
            include: {
              tickettypes: {
                include: {
                  events: {
                    include: {
                      locations: true
                    }
                  },
                  eventcategories: true
                }
              }
            }
          },
          tickets: true,
          users: {
            select: {
              first_name: true,
              last_name: true,
              phone_number: true
            }
          }
        }
      });

      if (!order) {
        throw new Error('Order not found');
      }

      return order;
    } catch (error) {
      console.error('Error getting order with tickets:', error);
      throw new Error('Failed to get order details');
    }
  }

  /**
   * Validate cart before order creation
   */
  async validateCartForOrder(accountId) {
    try {
      const userId = await this.getUserIdFromAccountId(accountId);

      const cartItems = await prisma.cart.findMany({
        where: { user_id: userId },
        include: {
          tickettypes: {
            select: {
              ticket_type_id: true,
              name: true,
              quantity_available: true,
              max_per_order: true
            }
          }
        }
      });

      if (cartItems.length === 0) {
        throw new Error('Cart is empty');
      }

      const validationErrors = [];

      for (const item of cartItems) {
        // Check availability
        if (item.tickettypes.quantity_available < item.quantity) {
          validationErrors.push(
            `Not enough tickets available for ${item.tickettypes.name}. Available: ${item.tickettypes.quantity_available}, Requested: ${item.quantity}`
          );
        }

        // Check max per order
        if (item.tickettypes.max_per_order && item.quantity > item.tickettypes.max_per_order) {
          validationErrors.push(
            `Maximum ${item.tickettypes.max_per_order} tickets allowed per order for ${item.tickettypes.name}`
          );
        }
      }

      if (validationErrors.length > 0) {
        throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
      }

      return { valid: true, cartItems };
    } catch (error) {
      console.error('Error validating cart:', error);
      throw error;
    }
  }

  /**
   * Process refund for order (mark tickets as refunded)
   */
  async processRefund(orderId, userId) {
    try {
      const order = await prisma.orders.findFirst({
        where: {
          order_id: orderId,
          user_id: userId
        },
        include: {
          tickets: true,
          orderitems: true
        }
      });

      if (!order) {
        throw new Error('Order not found');
      }

      if (order.payment_status === 'refunded') {
        throw new Error('Order already refunded');
      }

      // Update order status
      const updatedOrder = await prisma.orders.update({
        where: { order_id: orderId },
        data: { payment_status: 'refunded' }
      });

      // Restore ticket availability
      for (const orderItem of order.orderitems) {
        await prisma.tickettypes.update({
          where: { ticket_type_id: orderItem.ticket_type_id },
          data: {
            quantity_available: {
              increment: orderItem.quantity
            }
          }
        });
      }

      return updatedOrder;
    } catch (error) {
      console.error('Error processing refund:', error);
      throw new Error('Failed to process refund');
    }
  }
}

module.exports = OrderProcessingService;
