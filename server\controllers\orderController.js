const OrderService = require("../services/orderService");
const OrderProcessingService = require("../services/orderProcessingService");
const TicketService = require("../services/ticketService");
const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

class OrderController {
  constructor() {
    this.orderService = OrderService.getInstance();
    this.orderProcessingService = OrderProcessingService.getInstance();
    this.ticketService = TicketService.getInstance();
  }

  // Helper method to get user_id from req.user
  async getUserId(user) {
    // If we have roleId and it's a user, use it directly
    if (user.roleId && user.roleType === "user") {
      return user.roleId;
    }

    // If we have profile data, use user_id from profile
    if (user.profile && user.profile.user_id) {
      return user.profile.user_id;
    }

    // If we have accountId, look up the user_id from the users table
    if (user.accountId) {
      const userRecord = await prisma.users.findUnique({
        where: { account_id: user.accountId },
        select: { user_id: true },
      });

      if (userRecord) {
        return userRecord.user_id;
      }
    }

    // If no user_id can be found, throw an error
    throw new Error(
      "User profile not found. Please complete your profile setup."
    );
  }

  // Get user's orders
  getUserOrders = async (req, res) => {
    try {
      const { roleType } = req.user;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can view orders.",
        });
      }

      const userId = await this.getUserId(req.user);
      const orders = await this.orderService.getUserOrders(userId);

      res.status(200).json({
        success: true,
        message: "Orders fetched successfully",
        data: {
          orders,
        },
      });
    } catch (error) {
      console.error("Error fetching user orders:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch orders",
        data: null,
      });
    }
  };

  // Get user's tickets (formatted for dashboard)
  getUserTickets = async (req, res) => {
    try {
      const { roleType } = req.user;
      console.log("Fetching user tickets for user:", req.user);

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can view tickets.",
        });
      }

      const userId = await this.getUserId(req.user);
      console.log("Resolved user ID:", userId);
      const tickets = await this.orderService.getUserTickets(userId);

      res.status(200).json({
        success: true,
        message: "Tickets fetched successfully",
        data: {
          tickets,
        },
      });
    } catch (error) {
      console.error("Error fetching user tickets:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch tickets",
        data: null,
      });
    }
  };

  // Get specific order details
  getOrderById = async (req, res) => {
    try {
      const { roleType } = req.user;
      const { orderId } = req.params;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can view order details.",
        });
      }

      // Validate orderId
      const orderIdNum = parseInt(orderId);
      if (isNaN(orderIdNum)) {
        return res.status(400).json({
          success: false,
          message: "Invalid order ID",
        });
      }

      const userId = await this.getUserId(req.user);
      const order = await this.orderService.getOrderById(orderIdNum, userId);

      res.status(200).json({
        success: true,
        message: "Order details fetched successfully",
        data: {
          order,
        },
      });
    } catch (error) {
      console.error("Error fetching order details:", error);

      if (error.message === "Order not found") {
        return res.status(404).json({
          success: false,
          message: "Order not found",
        });
      }

      res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch order details",
        data: null,
      });
    }
  };

  // Get user order statistics
  getUserOrderStats = async (req, res) => {
    try {
      const { roleType } = req.user;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can view order statistics.",
        });
      }

      const userId = await this.getUserId(req.user);
      const stats = await this.orderService.getUserOrderStats(userId);

      res.status(200).json({
        success: true,
        message: "Order statistics fetched successfully",
        data: {
          stats,
        },
      });
    } catch (error) {
      console.error("Error fetching order statistics:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch order statistics",
        data: null,
      });
    }
  };

  // Create order from cart with attendee information
  createOrderFromCart = async (req, res) => {
    try {
      const { roleType } = req.user;
      const { attendeeInfo, paymentInfo } = req.body;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can create orders.",
        });
      }

      const accountId = req.user.accountId || req.user.id;

      if (!accountId) {
        return res.status(401).json({
          success: false,
          message: "User authentication required",
        });
      }

      if (!attendeeInfo || !Array.isArray(attendeeInfo)) {
        return res.status(400).json({
          success: false,
          message: "Attendee information is required",
        });
      }

      // Validate cart before processing
      await this.orderProcessingService.validateCartForOrder(accountId);

      // Create order from cart
      const orderResult = await this.orderProcessingService.createOrderFromCart(
        accountId,
        attendeeInfo,
        paymentInfo
      );

      res.status(201).json({
        success: true,
        message: "Order created successfully",
        data: {
          order: orderResult.order,
          tickets: orderResult.tickets,
          totalAmount: orderResult.totalAmount,
          subtotal: orderResult.subtotal,
          fees: orderResult.fees,
        },
      });
    } catch (error) {
      console.error("Error creating order from cart:", error);

      // Handle specific error cases
      if (
        error.message.includes("Cart is empty") ||
        error.message.includes("Validation failed") ||
        error.message.includes("Missing attendee information")
      ) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: error.message || "Failed to create order",
      });
    }
  };

  // Download ticket PDF
  downloadTicketPDF = async (req, res) => {
    try {
      const { roleType } = req.user;
      const { ticketId } = req.params;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can download tickets.",
        });
      }

      const userId = await this.getUserId(req.user);

      // Verify ticket belongs to user
      const ticket = await prisma.tickets.findFirst({
        where: {
          ticket_id: parseInt(ticketId),
          orders: {
            user_id: userId,
          },
        },
      });

      if (!ticket) {
        return res.status(404).json({
          success: false,
          message: "Ticket not found",
        });
      }

      // Download PDF from Supabase
      const pdfBuffer = await this.ticketService.downloadTicketPDF(
        parseInt(ticketId)
      );

      // Set response headers for PDF download
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="ticket-${ticketId}.pdf"`
      );
      res.setHeader("Content-Length", pdfBuffer.size);

      // Convert blob to buffer and send
      const arrayBuffer = await pdfBuffer.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      res.send(buffer);
    } catch (error) {
      console.error("Error downloading ticket PDF:", error);

      if (error.message.includes("not found")) {
        return res.status(404).json({
          success: false,
          message: "Ticket PDF not found",
        });
      }

      res.status(500).json({
        success: false,
        message: error.message || "Failed to download ticket PDF",
      });
    }
  };
}

module.exports = OrderController;
