{"version": 3, "file": "PDFParser.js", "sourceRoot": "", "sources": ["../../../src/core/parser/PDFParser.ts"], "names": [], "mappings": ";AAAA,OAAO,kBAAkB,uCAA6C;AACtE,OAAO,SAAS,8BAAoC;AACpD,OAAO,UAAU,+BAAqC;AACtD,OAAO,EACL,mBAAmB,EACnB,qBAAqB,EACrB,4BAA4B,EAC5B,YAAY,EACZ,kBAAkB,GACnB,kBAAwB;AACzB,OAAO,OAAO,2BAAiC;AAC/C,OAAO,gBAAgB,oCAA0C;AACjE,OAAO,OAAO,2BAAiC;AAE/C,OAAO,YAAY,gCAAsC;AACzD,OAAO,MAAM,0BAAgC;AAC7C,OAAO,UAAU,qBAAmC;AACpD,OAAO,eAAe,0BAAwC;AAC9D,OAAO,qBAAqB,gCAA8C;AAC1E,OAAO,mBAAmB,8BAA4C;AACtE,OAAO,UAAU,sBAA4B;AAC7C,OAAO,SAAS,4BAAkC;AAClD,OAAO,EAAE,QAAQ,EAAE,2BAAiC;AACpD,OAAO,EAAE,OAAO,EAAE,0BAAgC;AAClD,OAAO,EAAE,WAAW,EAAE,oBAAkB;AAExC;IAAwB,6BAAe;IAcrC,mBACE,QAAoB,EACpB,cAAyB,EACzB,oBAA4B,EAC5B,UAAkB;QAFlB,+BAAA,EAAA,yBAAyB;QACzB,qCAAA,EAAA,4BAA4B;QAC5B,2BAAA,EAAA,kBAAkB;QAJpB,YAME,kBAAM,UAAU,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,UAAU,CAAC,MAAM,EAAE,EAAE,UAAU,CAAC,SAGhE;QAZO,mBAAa,GAAG,KAAK,CAAC;QACtB,mBAAa,GAAG,CAAC,CAAC;QAqGlB,uBAAiB,GAAG;YAC1B,KAAI,CAAC,aAAa,IAAI,CAAC,CAAC;YACxB,OAAO,KAAI,CAAC,aAAa,GAAG,KAAI,CAAC,cAAc,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC;QA/FA,KAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,KAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;;IACnD,CAAC;IAEK,iCAAa,GAAnB;;;;;;wBACE,IAAI,IAAI,CAAC,aAAa,EAAE;4BACtB,MAAM,IAAI,YAAY,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;yBACtD;wBACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;wBAE1B,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;;;6BAGlC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;wBACvB,qBAAM,IAAI,CAAC,oBAAoB,EAAE,EAAA;;wBAAjC,SAAiC,CAAC;wBAC5B,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;wBACnC,IAAI,MAAM,KAAK,UAAU,EAAE;4BACzB,MAAM,IAAI,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;yBACrD;wBACD,UAAU,GAAG,MAAM,CAAC;;;wBAGtB,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBAExB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;4BACrC,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;4BAC9C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;yBACnC;wBAED,sBAAO,IAAI,CAAC,OAAO,EAAC;;;;KACrB;IAEO,oCAAgB,GAAxB;QACE,IAAM,cAAc,GAAG,UAAC,GAAe;YACrC,OAAA,GAAG,YAAY,OAAO;gBACtB,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,KAAK,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC;QADxD,CACwD,CAAC;QAE3D,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAEnE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;YAC5B,IAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE,CAAC;YAChE,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;gBAC1D,IAAA,KAAgB,eAAe,CAAC,GAAG,CAAC,EAAnC,GAAG,QAAA,EAAE,MAAM,QAAwB,CAAC;gBAC3C,IAAI,cAAc,CAAC,MAAM,CAAC,EAAE;oBAC1B,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,GAAG,GAAG,CAAC;iBACrC;aACF;SACF;IACH,CAAC;IAEO,+BAAW,GAAnB;QACE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE;YACzB,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBACtC,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBACxC,IAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjC,IAAM,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAClD,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,OAAO,MAAM,CAAC;aACf;YACD,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;SACnB;QAED,MAAM,IAAI,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;IACzD,CAAC;IAEO,6CAAyB,GAAjC;QACE,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAM,YAAY,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAExC,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAE5C,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACpC,MAAM,IAAI,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;SACpE;QAED,OAAO,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;IACnD,CAAC;IAEO,6CAAyB,GAAjC;QACE,IAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QAC1C,IAAI;YACF,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YACjC,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAOa,uCAAmB,GAAjC;;;;;;wBACQ,GAAG,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;wBAE7C,IAAI,CAAC,yBAAyB,EAAE,CAAC;wBAC3B,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;wBAElC,IAAI,CAAC,yBAAyB,EAAE,CAAC;wBACjC,6CAA6C;wBAC7C,yEAAyE;wBACzE,IAAI;wBAEJ,uCAAuC;wBACvC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;6BAGjC,CAAA,MAAM,YAAY,YAAY;4BAC9B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,KAAK,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAA,EAD/D,wBAC+D;wBAE/D,qBAAM,qBAAqB,CAAC,SAAS,CACnC,MAAM,EACN,IAAI,CAAC,iBAAiB,CACvB,CAAC,gBAAgB,EAAE,EAAA;;wBAHpB,SAGoB,CAAC;;;wBAChB,IACL,MAAM,YAAY,YAAY;4BAC9B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,KAAK,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,EAC7D;4BACA,mBAAmB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,CAAC;yBAC1D;6BAAM;4BACL,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;yBAClC;;4BAED,sBAAO,GAAG,EAAC;;;;KACZ;IAED,kCAAkC;IAC1B,mDAA+B,GAAvC;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;QAEvC,IAAM,GAAG,GAAG,qCAAmC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAG,CAAC;QAC3E,IAAI,IAAI,CAAC,oBAAoB;YAAE,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QACpD,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAElB,IAAM,GAAG,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAE7C,OAAO,CAAC,IAAI,CAAC,yBAAuB,GAAK,CAAC,CAAC;QAE3C,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QAElC,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE;YACzB,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBACtC,MAAM,GAAG,KAAK,CAAC;aAChB;YACD,IAAI,CAAC,MAAM;gBAAE,MAAM;YACnB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;SACnB;QAED,IAAI,MAAM;YAAE,MAAM,IAAI,4BAA4B,CAAC,QAAQ,CAAC,CAAC;QAE7D,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;QAEzD,IAAM,MAAM,GAAG,gBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;QACjE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAEjC,OAAO,GAAG,CAAC;IACb,CAAC;IAEa,wCAAoB,GAAlC;;;;;;wBACE,IAAI,CAAC,yBAAyB,EAAE,CAAC;;;6BAE1B,CAAA,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAA;wBAC/C,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;;;;wBAGxC,qBAAM,IAAI,CAAC,mBAAmB,EAAE,EAAA;;wBAAhC,SAAgC,CAAC;;;;wBAEjC,kEAAkE;wBAClE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;wBACjC,IAAI,CAAC,+BAA+B,EAAE,CAAC;;;wBAEzC,IAAI,CAAC,yBAAyB,EAAE,CAAC;wBAEjC,yEAAyE;wBACzE,IAAI,CAAC,aAAa,EAAE,CAAC;6BAEjB,IAAI,CAAC,iBAAiB,EAAE,EAAxB,wBAAwB;wBAAE,qBAAM,WAAW,EAAE,EAAA;;wBAAnB,SAAmB,CAAC;;;;;;;KAErD;IAEO,6CAAyB,GAAjC;QACE,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;YAAE,OAAO;QAC9C,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEjC,IAAI,YAAY,GAAG,CAAC,CAAC,CAAC;QACtB,IAAM,IAAI,GAAG,kBAAkB,CAAC,WAAW,EAAE,CAAC;QAE9C,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE;YACvD,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAEjC,IAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACrC,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAEjC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAC/B,IAAI,IAAI,KAAK,SAAS,CAAC,CAAC,IAAI,IAAI,KAAK,SAAS,CAAC,CAAC,EAAE;gBAChD,IAAM,GAAG,GAAG,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;gBAC/C,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,SAAS,CAAC,CAAC,EAAE;oBACrC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;iBAC9B;qBAAM;oBACL,4BAA4B;oBAC5B,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;iBACrC;gBACD,YAAY,IAAI,CAAC,CAAC;aACnB;iBAAM;gBACL,YAAY,GAAG,QAAQ,CAAC;aACzB;YACD,IAAI,CAAC,yBAAyB,EAAE,CAAC;SAClC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,yCAAqB,GAA7B;QACE,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO;QACjD,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEjC,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEtB,IAAA,OAAO,GAAK,IAAI,QAAT,CAAU;QACzB,OAAO,CAAC,WAAW,GAAG;YACpB,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI;YAC9D,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO;YACvE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI;YAC9D,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,EAAE;SACzD,CAAC;IACJ,CAAC;IAEO,qCAAiB,GAAzB;QACE,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO;QACnD,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEjC,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAElC,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEjC,OAAO,UAAU,CAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC;IACzD,CAAC;IAEa,wCAAoB,GAAlC;;;;4BACE,qBAAM,IAAI,CAAC,oBAAoB,EAAE,EAAA;;wBAAjC,SAAiC,CAAC;wBAClC,IAAI,CAAC,yBAAyB,EAAE,CAAC;wBACjC,IAAI,CAAC,qBAAqB,EAAE,CAAC;wBAC7B,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBAEzB,yEAAyE;wBACzE,IAAI,CAAC,aAAa,EAAE,CAAC;;;;;KACtB;IAED;;;;;OAKG;IACK,iCAAa,GAArB;QACE,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE;YACzB,IAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAC1C,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAC/B,IAAM,cAAc,GAAG,IAAI,IAAI,SAAS,CAAC,KAAK,IAAI,IAAI,IAAI,SAAS,CAAC,KAAK,CAAC;YAC1E,IAAI,cAAc,EAAE;gBAClB,IACE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;oBAChC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC;oBACnC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC;oBACrC,IAAI,CAAC,yBAAyB,EAAE,EAChC;oBACA,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;oBACjC,MAAM;iBACP;aACF;YACD,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;SACnB;IACH,CAAC;IAED;;;;;;;;;;;;OAYG;IACK,2CAAuB,GAA/B;QACE,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI;YACF,IAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAC1C,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;SAClC;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAClB,IAAI,CAAC,yBAAyB,EAAE,CAAC;SAClC;IACH,CAAC;IA7UM,6BAAmB,GAAG,UAC3B,QAAoB,EACpB,cAAuB,EACvB,oBAA8B,EAC9B,UAAoB;QAEpB,OAAA,IAAI,SAAS,CAAC,QAAQ,EAAE,cAAc,EAAE,oBAAoB,EAAE,UAAU,CAAC;IAAzE,CAAyE,CAAC;IAwU9E,gBAAC;CAAA,AA/UD,CAAwB,eAAe,GA+UtC;AAED,eAAe,SAAS,CAAC"}