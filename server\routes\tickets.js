const express = require('express');
const OrderController = require('../controllers/orderController');
const { verifyTokenFromCookie } = require('../middleware/jwtCookieMiddleware');

const router = express.Router();
const orderController = new OrderController();

// All ticket routes require authentication
router.use(verifyTokenFromCookie);

// Download ticket PDF
// GET /api/tickets/:ticketId/download
router.get('/:ticketId/download', orderController.downloadTicketPDF);

module.exports = router;
