{"version": 3, "file": "BaseParser.d.ts", "sourceRoot": "", "sources": ["../../../src/core/parser/BaseParser.ts"], "names": [], "mappings": "AACA,OAAO,UAAU,qBAAmC;AASpD,cAAM,UAAU;IACd,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,CAAC;IACrC,SAAS,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC;gBAE3B,KAAK,EAAE,UAAU,EAAE,UAAU,UAAQ;IAKjD,SAAS,CAAC,WAAW,IAAI,MAAM;IAoB/B,SAAS,CAAC,cAAc,IAAI,MAAM;IAsClC,SAAS,CAAC,cAAc,IAAI,IAAI;IAMhC,SAAS,CAAC,QAAQ,IAAI,IAAI;IAQ1B,SAAS,CAAC,WAAW,IAAI,OAAO;IAUhC,SAAS,CAAC,yBAAyB,IAAI,IAAI;IAK3C,SAAS,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,OAAO;CAUnD;AAED,eAAe,UAAU,CAAC"}