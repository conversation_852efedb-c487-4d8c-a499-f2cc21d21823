{"version": 3, "file": "Ascii85Stream.js", "sourceRoot": "", "sources": ["../../../src/core/streams/Ascii85Stream.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;;AAEH,OAAO,YAAY,uBAAsC;AAGzD,IAAM,OAAO,GAAG,UAAC,EAAU;IACzB,OAAA,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI;AAAxD,CAAwD,CAAC;AAE3D;IAA4B,iCAAY;IAItC,uBAAY,MAAkB,EAAE,WAAoB;QAApD,YACE,kBAAM,WAAW,CAAC,SAUnB;QARC,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,KAAI,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;QAE/B,kEAAkE;QAClE,4BAA4B;QAC5B,IAAI,WAAW,EAAE;YACf,WAAW,GAAG,GAAG,GAAG,WAAW,CAAC;SACjC;;IACH,CAAC;IAES,iCAAS,GAAnB;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,CAAC,MAAM;QAC/B,IAAM,YAAY,GAAG,IAAI,CAAC,CAAC,MAAM;QACjC,IAAM,GAAG,GAAG,CAAC,CAAC,CAAC;QAEf,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAE3B,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;QACzB,OAAO,OAAO,CAAC,CAAC,CAAC,EAAE;YACjB,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;SACtB;QAED,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,UAAU,EAAE;YACjC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;YAChB,OAAO;SACR;QAED,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,IAAI,MAAM,CAAC;QACX,IAAI,CAAC,CAAC;QAEN,qBAAqB;QACrB,IAAI,CAAC,KAAK,YAAY,EAAE;YACtB,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;YAC7C,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;gBACtB,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;aAC9B;YACD,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC;SACxB;aAAM;YACL,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACzB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACb,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;gBACtB,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,CAAC,CAAC,EAAE;oBACjB,CAAC,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;iBACtB;gBAED,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBAEb,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,UAAU,EAAE;oBACjC,MAAM;iBACP;aACF;YACD,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACjD,IAAI,CAAC,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC;YAE3B,kBAAkB;YAClB,IAAI,CAAC,GAAG,CAAC,EAAE;gBACT,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;oBACjB,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;iBACtB;gBACD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;aACjB;YACD,IAAI,CAAC,GAAG,CAAC,CAAC;YACV,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;gBACtB,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;aAChC;YAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;gBACvB,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;gBACpC,CAAC,KAAK,CAAC,CAAC;aACT;SACF;IACH,CAAC;IACH,oBAAC;AAAD,CAAC,AAjFD,CAA4B,YAAY,GAiFvC;AAED,eAAe,aAAa,CAAC"}