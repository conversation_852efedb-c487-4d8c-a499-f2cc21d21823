{"version": 3, "file": "ByteStream.js", "sourceRoot": "", "sources": ["../../../src/core/parser/ByteStream.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,kBAAwB;AAEzD,OAAO,EAAE,kBAAkB,EAAE,0BAAgC;AAC7D,OAAO,SAAS,4BAAkC;AAElD,sDAAsD;AACtD;IAaE,oBAAY,KAAiB;QAJrB,QAAG,GAAG,CAAC,CAAC;QACR,SAAI,GAAG,CAAC,CAAC;QACT,WAAM,GAAG,CAAC,CAAC;QAGjB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAClC,CAAC;IAED,2BAAM,GAAN,UAAO,MAAc;QACnB,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;IACpB,CAAC;IAED,yBAAI,GAAJ;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QACpC,IAAI,IAAI,KAAK,SAAS,CAAC,OAAO,EAAE;YAC9B,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;YACf,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;SACjB;aAAM;YACL,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;SAClB;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,+BAAU,GAAV,UAAW,QAAgB;QACzB,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,QAAQ,EAAE;YAC5B,MAAM,IAAI,sBAAsB,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;SAC1E;QACD,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;IACrB,CAAC;IAED,yBAAI,GAAJ;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAED,8BAAS,GAAT,UAAU,KAAa;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;IACtC,CAAC;IAED,2BAAM,GAAN,UAAO,MAAc;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAED,yBAAI,GAAJ;QACE,OAAO,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC;IACjC,CAAC;IAED,2BAAM,GAAN;QACE,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IAED,0BAAK,GAAL,UAAM,KAAa,EAAE,GAAW;QAC9B,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACtC,CAAC;IAED,6BAAQ,GAAR;QACE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;IACpE,CAAC;IAjEM,aAAE,GAAG,UAAC,KAAiB,IAAK,OAAA,IAAI,UAAU,CAAC,KAAK,CAAC,EAArB,CAAqB,CAAC;IAElD,2BAAgB,GAAG,UAAC,SAAuB;QAChD,OAAA,UAAU,CAAC,EAAE,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,CAAC;IAArD,CAAqD,CAAC;IA+D1D,iBAAC;CAAA,AAnED,IAmEC;AAED,eAAe,UAAU,CAAC"}