{"version": 3, "file": "PDFParser.d.ts", "sourceRoot": "", "sources": ["../../../src/core/parser/PDFParser.ts"], "names": [], "mappings": "AAiBA,OAAO,eAAe,0BAAwC;AAG9D,OAAO,UAAU,sBAA4B;AAM7C,cAAM,SAAU,SAAQ,eAAe;IACrC,MAAM,CAAC,mBAAmB,aACd,UAAU,kIAKsD;IAE5E,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAS;IACxC,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAU;IAC/C,OAAO,CAAC,aAAa,CAAS;IAC9B,OAAO,CAAC,aAAa,CAAK;gBAGxB,QAAQ,EAAE,UAAU,EACpB,cAAc,SAAW,EACzB,oBAAoB,UAAQ,EAC5B,UAAU,UAAQ;IAOd,aAAa,IAAI,OAAO,CAAC,UAAU,CAAC;IA4B1C,OAAO,CAAC,gBAAgB;IAkBxB,OAAO,CAAC,WAAW;IAgBnB,OAAO,CAAC,yBAAyB;IAejC,OAAO,CAAC,yBAAyB;IAWjC,OAAO,CAAC,iBAAiB,CAGvB;YAEY,mBAAmB;IAmCjC,OAAO,CAAC,+BAA+B;YAiCzB,oBAAoB;IAsBlC,OAAO,CAAC,yBAAyB;IAkCjC,OAAO,CAAC,qBAAqB;IAgB7B,OAAO,CAAC,iBAAiB;YAgBX,oBAAoB;IAUlC;;;;;OAKG;IACH,OAAO,CAAC,aAAa;IAqBrB;;;;;;;;;;;;OAYG;IACH,OAAO,CAAC,uBAAuB;CAWhC;AAED,eAAe,SAAS,CAAC"}